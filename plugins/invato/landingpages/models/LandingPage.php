<?php namespace Invato\Landingpages\Models;

use Model;
use Cms\Classes\Controller;
use Cms\Classes\Theme;
use Cms\Classes\Page as CmsPage;

/**
 * Model
 */
class LandingPage extends Model
{
    use \October\Rain\Database\Traits\Validation;
    use \October\Rain\Database\Traits\SoftDelete;
    use \October\Rain\Database\Traits\Sluggable;
    use \October\Rain\Database\Traits\Sortable;

    protected $slugs = ['slug' => 'title'];
    protected $fillable = ['title', 'slug', 'page_url'];


    /**
     * @var array dates to cast from the database.
     */
    protected $dates = ['deleted_at'];

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_landingpages_pages';

    /**
     * @var array rules for validation.
     */
    public $rules = [
        'title' => ['required'],
        'slug' => ['required'],
        'page_url' => ['required'],
    ];

    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'landingpage') {
            $references = [];

            $landingpages = self::orderBy('title')->get();
            foreach ($landingpages as $page) {
                $references[$page->id] = $page->title;
            }

            $result = [
                'references'   => $references,
                'nesting'      => false,
                'dynamicItems' => false
            ];
        }


        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (!$page->hasComponent('renderLandingPage')) {
                    continue;
                }

                $properties = $page->getComponentProperties('renderLandingPage');
                if (!preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }



    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'landingpage') {
            $model = LandingPage::find($item->reference);

            if (!$model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        }


        return $result;
    }

}
