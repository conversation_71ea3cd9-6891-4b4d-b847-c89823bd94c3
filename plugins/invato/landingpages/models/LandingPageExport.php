<?php namespace Invato\Landingpages\Models;

use Exception;

class LandingPageExport extends \Backend\Models\ExportModel
{
    public function exportData($columns, $sessionKey = null)
    {
        $landingpages = LandingPage::all();

        $landingpages->each(function($landingpage) use ($columns) {
            $landingpage->addVisible($columns);
        });

        return $landingpages->toArray();
    }
}
