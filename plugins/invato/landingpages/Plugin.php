<?php namespace Invato\Landingpages;

use System\Classes\PluginBase;
use Event;
use Invato\Landingpages\Models\LandingPage;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    /**
     * boot method, called right before the request route.
     */
    public function boot()
    {
        $this->registerPageFinder();
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
        return [
            \Invato\Landingpages\Components\RenderLandingPage::class => 'renderLandingPage'
        ];
    }

    protected function registerPageFinder(): void
    {
        $listTypes = function () {
            return [
                'landingpage' => 'Landingspagina',
            ];
        };

        $getTypeInfo = function ($type) {
            if ($type == 'landingpage') {
                return LandingPage::getMenuTypeInfo($type);
            }

            return [];
        };

        $resolveItem = function($type, $item, $url, $theme) {
            if ($type == 'landingpage') {
                return LandingPage::resolveMenuItem($item, $url, $theme);
            }

            return null;
        };

        Event::listen([
            'cms.pageLookup.listTypes',
            'pages.menuitem.listTypes'
        ], $listTypes);

        Event::listen([
            'cms.pageLookup.getTypeInfo',
            'pages.menuitem.getTypeInfo'
        ], $getTypeInfo);

        Event::listen([
            'cms.pageLookup.resolveItem',
            'pages.menuitem.resolveItem'
        ], $resolveItem);

    }
}
