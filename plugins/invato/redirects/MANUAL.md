# Invato Redirects Plugin - User Manual

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Managing Redirects](#managing-redirects)
4. [Import/Export](#importexport)
5. [Permissions](#permissions)
6. [Troubleshooting](#troubleshooting)

## Introduction

The Invato Redirects Plugin allows you to manage URL redirects in your OctoberCMS website. This is essential for maintaining SEO rankings when URLs change and providing a better user experience.

## Getting Started

### Accessing the Plugin

1. Log into your OctoberCMS backend
2. Navigate to **Redirects** in the main menu
3. Click on **Redirects** to view the redirect management interface

### Understanding Redirect Types

- **301 Redirect**: Permanent redirect (recommended for SEO)
- **302 Redirect**: Temporary redirect

## Managing Redirects

### Creating a New Redirect

1. Click the **Create** button in the redirects list
2. Fill in the required fields:
   - **Old URL**: The URL you want to redirect from
   - **New URL**: The destination URL
   - **Status**: Choose 301 (permanent) or 302 (temporary)
   - **Comment**: Optional description for internal reference

3. Click **Save** to create the redirect

### Editing Redirects

1. Click on any redirect in the list to edit it
2. Modify the fields as needed
3. Click **Save** to apply changes

### Deleting Redirects

1. Select one or more redirects using the checkboxes
2. Click **Delete selected** button
3. Confirm the deletion

### Restoring Deleted Redirects

1. Enable **Show deleted** filter in the list
2. Select deleted redirects
3. Click **Restore selected** button

## Import/Export

### Exporting Redirects

1. Click the **Export** button in the toolbar
2. Choose your export format (CSV)
3. Download the generated file

### Importing Redirects

1. Click the **Import** button in the toolbar
2. Upload your CSV file
3. Map the columns to the appropriate fields
4. Review and confirm the import

### CSV Format

Your CSV file should include these columns:
- `old_url`: The source URL
- `new_url`: The destination URL  
- `status`: HTTP status code (301 or 302)
- `comment`: Optional description

## Permissions

The plugin uses the following permission system:

### Available Permissions

- **Manage Redirects** (`invato.redirects.manage_redirects`)
  - Create, edit, delete redirects
  - View redirect list

- **Import Redirects** (`invato.redirects.import_redirects`)
  - Upload and import redirect data

- **Export Redirects** (`invato.redirects.export_redirects`)
  - Download redirect data

### Assigning Permissions

1. Go to **Settings** > **Administrators** > **Manage Roles**
2. Edit or create a role
3. Find the **Redirects** permission tab
4. Check the appropriate permissions
5. Save the role

## Troubleshooting

### Common Issues

**Redirect not working:**
- Check that the old URL is exactly correct
- Ensure the redirect status is appropriate (301/302)
- Verify the new URL is accessible

**Import fails:**
- Check CSV format matches requirements
- Ensure all required fields are present
- Verify file encoding is UTF-8

**Permission denied:**
- Check user has appropriate permissions
- Contact administrator to assign correct role

### Getting Help

If you encounter issues not covered in this manual:

1. Check the system logs in **Settings** > **System** > **Event Log**
2. Contact your system administrator
3. Refer to the plugin README for technical details

## Best Practices

### SEO Considerations

- Use 301 redirects for permanent URL changes
- Use 302 redirects only for temporary changes
- Avoid redirect chains (A→B→C)
- Test redirects after implementation

### Maintenance

- Regularly review and clean up old redirects
- Use comments to document why redirects were created
- Export redirects as backup before major changes
- Monitor redirect performance and update as needed
