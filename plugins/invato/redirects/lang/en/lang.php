<?php

return [
    'plugin' => [
        'name' => 'Redirects',
        'description' => 'Manage URL redirects with advanced features',
    ],
    'redirects' => [
        'old_url' => 'Old URL',
        'old_url_comment' => '/old/old-page',
        'new_url' => 'New URL',
        'new_url_comment' => '/new/new-page',
        'status' => 'Status',
        'status_comment' => '301 (permanent) or 302 (temporary)',
        'comment' => 'Comment',
        'created_at' => 'Created at',
        'updated_at' => 'Updated at',
        'deleted_at' => 'Deleted at',
    ],
    'redirect' => [
        'duplicate_confirm' => 'Are you sure you want to duplicate this redirect?',
        'restore_confirm' => 'Are you sure you want to restore this redirect?',
    ],
    'permissions' => [
        'manage_redirects' => 'Manage redirects',
        'import_redirects' => 'Import redirects',
        'export_redirects' => 'Export redirects',
    ],
    'global' => [
        'actions' => 'Actions',
        'duplicate' => 'Duplicate',
        'restore' => 'Restore',
        'delete' => 'Delete',
        'import' => 'Import',
        'export' => 'Export',
        'delete_selected' => 'Delete selected',
        'restore_selected' => 'Restore selected',
        'delete_selected_confirm' => 'Are you sure you want to delete the selected redirects?',
        'restore_selected_confirm' => 'Are you sure you want to restore the selected redirects?',
        'show_deleted' => 'Show deleted',
    ],
    'menu' => [
        'content' => 'Content',
        'redirects' => 'Redirects',
        'documentation' => 'Documentation',
        'readme' => 'README',
        'manual' => 'Manual',
    ],
    'menuitem' => [
        'redirect_detail' => 'Redirect Detail',
        'all_redirects' => 'All Redirects',
    ],
];
