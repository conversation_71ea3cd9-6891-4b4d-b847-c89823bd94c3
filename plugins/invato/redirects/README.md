# Invato Redirects Plugin

This plugin provides comprehensive redirect functionality for OctoberCMS v3.6+ with advanced management features.

## Features

- **URL Redirects Management**: Create and manage 301/302 redirects
- **Soft Delete Support**: Safely delete and restore redirects
- **Import/Export Functionality**: Bulk operations for redirect management
- **Multilingual Support**: Dutch and English translations
- **Permission System**: Granular access control
- **Middleware Integration**: Automatic redirect processing

## Installation

1. Install via Composer:
```bash
composer require invato/redirects-plugin
```

2. Run the plugin migrations:
```bash
php artisan october:migrate
```

## Usage

### Creating Redirects

Navigate to **Redirects** > **Redirects** in the backend to manage your redirects.

### Import/Export

Use the import/export functionality to manage redirects in bulk:
- Export existing redirects to CSV
- Import redirects from CSV files

### Permissions

The plugin includes the following permissions:

- `invato.redirects.manage_redirects` - Manage redirects
- `invato.redirects.import_redirects` - Import redirects
- `invato.redirects.export_redirects` - Export redirects

## Technical Details

### Models

- **Redirect**: Main model for URL redirects with soft delete support
- **RedirectImport**: Handles CSV import functionality
- **RedirectExport**: Handles CSV export functionality

### Controllers

- **Redirects**: Main CRUD controller with import/export support
- **Readme**: Documentation controller
- **Manual**: User manual controller

### Traits

- **CanRedirectModelTrait**: Core redirect functionality
- **HasPageFinderTrait**: Page finder integration
- **PluginSoftDeleteTrait**: Soft delete operations
- **PluginImportExportTrait**: Import/export operations
- **PluginDuplicateTrait**: Record duplication

## Requirements

- OctoberCMS v3.6+
- PHP 8.3+
- MySQL 8.0+

## License

This plugin is proprietary software developed by Invato.
