<?php

namespace Invato\Redirects\Models;

use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class Redirect extends Model
{
    // BEGIN Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use SoftDelete;
    use Validation;

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_redirects_redirects';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'old_url' => 'string',
        'new_url' => 'string',
        'status' => 'integer',
        'comment' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'old_url',
        'new_url',
        'status',
        'comment',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'old_url' => ['required', 'string', 'max:255'],
        'new_url' => ['required', 'string', 'max:255'],
        'status' => ['required', 'integer', 'in:301,302'],
        'comment' => ['nullable', 'string'],
    ];

    // translatable
    public $implement = [
        \RainLab\Translate\Behaviors\TranslatableModel::class
    ];
    public $translatable = [
        'comment',
    ];

    /**
     * Get PageFinder configuration for Redirect model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'redirect-detail',
            'all_type' => 'all-redirects',
            'component' => 'RedirectDetail',
        ];
    }

    // END Skeleton model

    // BEGIN Model specific
    // END Model specific
}
