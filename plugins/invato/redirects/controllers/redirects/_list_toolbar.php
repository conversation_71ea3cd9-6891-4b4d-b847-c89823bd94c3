<?php

use Backend\Facades\BackendAuth;

$pluginUri = 'redirects';
$modelUri = 'redirects';
?>

<div data-control="toolbar">
    <!-- Create button -->
    <a
        href="<?= Backend::url("invato/{$pluginUri}/{$modelUri}/create") ?>"
        class="btn btn-primary oc-icon-plus">
        <?= e(trans('backend::lang.form.create')) ?>
    </a>

    <?php if (BackendAuth::getUser()->hasAccess("invato.{$pluginUri}.export_{$modelUri}")) { ?>
        <!-- Export button -->
        <a
            href="<?= Backend::url("invato/{$pluginUri}/{$modelUri}/export") ?>"
            class="btn btn-default oc-icon-download">
            <?= e(trans('invato.redirects::lang.global.export')) ?>
        </a>
    <?php } ?>

    <?php if (BackendAuth::getUser()->hasAccess("invato.{$pluginUri}.import_{$modelUri}")) { ?>
        <!-- Import button -->
        <a
            href="<?= Backend::url("invato/{$pluginUri}/{$modelUri}/import") ?>"
            class="btn btn-default oc-icon-upload">
            <?= e(trans('invato.redirects::lang.global.import')) ?>
        </a>
    <?php } ?>

    <!-- Delete selected button -->
    <button
        class="btn btn-danger oc-icon-trash-o"
        data-request="onDelete"
        data-request-confirm="<?= e(trans('invato.redirects::lang.global.delete_selected_confirm')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('invato.redirects::lang.global.delete_selected')) ?>
    </button>

    <!-- Restore selected button -->
    <button
        class="btn btn-success oc-icon-refresh"
        data-request="onRestoreSelected"
        data-request-confirm="<?= e(trans('invato.redirects::lang.global.restore_selected_confirm')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('invato.redirects::lang.global.restore_selected')) ?>
    </button>
</div>
