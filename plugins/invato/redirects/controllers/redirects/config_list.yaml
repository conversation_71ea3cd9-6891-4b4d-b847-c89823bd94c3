list: $/invato/redirects/models/redirect/columns.yaml
modelClass: Invato\Redirects\Models\Redirect
title: Redirects
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/redirects/redirects/update/:id'
structure:
    showTree: false
    showReorder: false
    showSorting: false
# BEGIN Skeleton Soft Deletes
filter: $/invato/redirects/models/redirect/scopes.yaml
# END Skeleton Soft Deletes
