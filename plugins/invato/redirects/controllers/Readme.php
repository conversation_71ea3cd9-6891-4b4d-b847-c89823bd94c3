<?php

namespace Invato\Redirects\Controllers;

use Backend\Classes\Controller;
use BackendMenu;
use Invato\Traits\SettingsMenuContextTrait;

class Readme extends Controller
{
    use SettingsMenuContextTrait;

    public $requiredPermissions = [
        'superusers.view_readme',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Redirects', 'redirects', 'readme');
    }

    public function index()
    {
        $this->pageTitle = 'README';
        $this->vars['content'] = $this->getReadmeContent();
    }

    protected function getReadmeContent(): string
    {
        $readmePath = plugins_path('invato/redirects/README.md');
        
        if (!file_exists($readmePath)) {
            return 'README.md file not found.';
        }

        return file_get_contents($readmePath);
    }
}
