<?php Block::put('breadcrumb') ?>
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= Backend::url('invato/redirects/redirects') ?>">Redirects</a></li>
        <li class="breadcrumb-item active" aria-current="page">README</li>
    </ol>
<?php Block::endPut() ?>

<div class="layout">
    <div class="layout-row">
        <div class="layout-cell">
            <div class="form-preview">
                <div class="form-preview-content">
                    <div class="markdown-content">
                        <?= \Markdown::parse($content) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.markdown-content {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
}

.markdown-content h1 {
    font-size: 2em;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 10px;
}

.markdown-content h2 {
    font-size: 1.5em;
    border-bottom: 1px solid #eaecef;
    padding-bottom: 8px;
}

.markdown-content code {
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    font-size: 85%;
    margin: 0;
    padding: 0.2em 0.4em;
}

.markdown-content pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    font-size: 85%;
    line-height: 1.45;
    overflow: auto;
    padding: 16px;
}

.markdown-content pre code {
    background-color: transparent;
    border: 0;
    display: inline;
    line-height: inherit;
    margin: 0;
    max-width: auto;
    overflow: visible;
    padding: 0;
    word-wrap: normal;
}

.markdown-content ul,
.markdown-content ol {
    padding-left: 2em;
}

.markdown-content blockquote {
    border-left: 0.25em solid #dfe2e5;
    color: #6a737d;
    padding: 0 1em;
}

.markdown-content table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

.markdown-content table th,
.markdown-content table td {
    border: 1px solid #dfe2e5;
    padding: 6px 13px;
}

.markdown-content table th {
    background-color: #f6f8fa;
    font-weight: 600;
}
</style>
