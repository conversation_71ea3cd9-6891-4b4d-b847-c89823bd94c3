<?php

namespace Invato\Redirects\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Redirects\Models\Redirect;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Redirects extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Redirect::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.redirects.import_redirects';

    public string $exportPermission = 'invato.redirects.export_redirects';

    public $requiredPermissions = [
        'invato.redirects.manage_redirects',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Redirects', 'main-menu-item', 'redirects');
    }

    // START Redirects Specific function.

    // END Redirects Specific function.
}
