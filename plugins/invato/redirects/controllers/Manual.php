<?php

namespace Invato\Redirects\Controllers;

use Backend\Classes\Controller;
use BackendMenu;
use Invato\Traits\SettingsMenuContextTrait;

class Manual extends Controller
{
    use SettingsMenuContextTrait;

    public $requiredPermissions = [
        'invato.redirects.manage_redirects',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Redirects', 'redirects', 'manual');
    }

    public function index()
    {
        $this->pageTitle = 'Manual';
        $this->vars['content'] = $this->getManualContent();
    }

    protected function getManualContent(): string
    {
        $manualPath = plugins_path('invato/redirects/MANUAL.md');
        
        if (!file_exists($manualPath)) {
            return 'MANUAL.md file not found.';
        }

        return file_get_contents($manualPath);
    }
}
